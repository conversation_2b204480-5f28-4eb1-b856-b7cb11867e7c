<template>
  <div v-if="visible" class="dialog-overlay">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <h3>{{ isEdit ? '编辑门店' : '新增门店' }}</h3>
        <button class="close-btn" @click="handleClose">×</button>
      </div>
      
      <div class="dialog-body">
        <!-- Tab 导航 -->
        <div class="tab-navigation">
          <button 
            class="tab-btn" 
            :class="{ active: activeTab === 'basic' }" 
            @click="activeTab = 'basic'"
          >
            <span class="tab-icon">📋</span>
            基本信息
          </button>
          <button 
            class="tab-btn" 
            :class="{ active: activeTab === 'hours' }" 
            @click="activeTab = 'hours'"
          >
            <span class="tab-icon">🕐</span>
            营业时间
          </button>
        </div>

        <!-- Tab 内容 -->
        <div class="tab-content" style="min-height: 400px; background: white; display: block;">

          <!-- 基本信息 Tab -->
          <div v-if="activeTab === 'basic'" class="tab-panel" style="display: block !important; visibility: visible !important; opacity: 1 !important;">
              <div class="form-section">
              <h4 class="section-title">门店基本信息</h4>
              <div class="form-row">
                <div class="form-field">
                  <label class="field-label">门店名称 <span class="required">*</span></label>
                  <input 
                    v-model="formData.name" 
                    type="text" 
                    class="form-input" 
                    placeholder="请输入门店名称"
                  >
                </div>
                <div class="form-field">
                  <label class="field-label">门店负责人 <span class="required">*</span></label>
                  <input 
                    v-model="formData.manager" 
                    type="text" 
                    class="form-input" 
                    placeholder="请输入负责人姓名"
                  >
                </div>
                <div class="form-field">
                  <label class="field-label">营业状态</label>
                  <select v-model="formData.status" class="form-select">
                    <option value="active">营业中</option>
                    <option value="pending">待开业</option>
                  </select>
                </div>
              </div>
            </div>

            <div class="form-section">
              <h4 class="section-title">联系方式</h4>
              <div class="form-row">
                <div class="form-field">
                  <label class="field-label">登录手机 <span class="required">*</span></label>
                  <input 
                    v-model="formData.phone" 
                    type="tel" 
                    class="form-input" 
                    placeholder="请输入登录手机号"
                  >
                </div>
                <div class="form-field">
                  <label class="field-label">登录密码</label>
                  <input 
                    v-model="formData.password" 
                    type="password" 
                    class="form-input" 
                    placeholder="请输入登录密码"
                  >
                </div>
                <div class="form-field">
                  <label class="field-label">服务电话</label>
                  <input 
                    v-model="formData.servicePhone" 
                    type="tel" 
                    class="form-input" 
                    placeholder="请输入客服电话"
                  >
                </div>
              </div>
            </div>

            <div class="form-section">
              <h4 class="section-title">门店位置</h4>
              <div class="form-row">
                <div class="form-field full-width">
                  <label class="field-label">门店地址 <span class="required">*</span></label>
                  <div class="address-input-group">
                    <input 
                      v-model="formData.address" 
                      type="text" 
                      class="form-input" 
                      placeholder="请输入详细地址"
                    >
                    <button class="location-btn" type="button" v-if="false">
                      📍 选择位置
                    </button>
                  </div>
                </div>
              </div>
              <div class="form-row" v-if="false">
                <div class="form-field">
                  <label class="field-label">经度坐标</label>
                  <input 
                    v-model="formData.longitude" 
                    type="text" 
                    class="form-input" 
                    placeholder="longitude"
                  >
                </div>
                <div class="form-field">
                  <label class="field-label">纬度坐标</label>
                  <input 
                    v-model="formData.latitude" 
                    type="text" 
                    class="form-input" 
                    placeholder="latitude"
                  >
                </div>
              </div>
            </div>

            <div class="form-section">
              <h4 class="section-title">备注信息</h4>
              <div class="form-row">
                <div class="form-field full-width">
                  <label class="field-label">备注说明</label>
                  <textarea 
                    v-model="formData.description" 
                    class="form-textarea" 
                    placeholder="请输入备注说明"
                    rows="4"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <!-- 营业时间 Tab -->
          <div v-if="activeTab === 'hours'" class="tab-panel" style="display: block !important; visibility: visible !important; opacity: 1 !important;">
              <div class="form-section">
              <h4 class="section-title">营业时间设置</h4>
              <p class="section-desc">设置门店每天的营业时间，可以单独设置某天为休息日</p>
              
              <div class="business-hours-list">
                <div 
                  v-for="day in weekDays" 
                  :key="day.key" 
                  class="hours-row"
                >
                  <div class="day-label">{{ day.label }}</div>
                  <div class="hours-controls">
                    <span class="status-tag" :class="day.isOpen ? 'active' : 'closed'">
                      {{ day.isOpen ? '营业' : '休息' }}
                    </span>
                    <template v-if="day.isOpen">
                      <input 
                        v-model="day.startTime" 
                        type="time" 
                        class="time-input"
                      >
                      <span class="time-separator">至</span>
                      <input 
                        v-model="day.endTime" 
                        type="time" 
                        class="time-input"
                      >
                    </template>
                    <div class="toggle-switch" :class="{ active: day.isOpen }" @click="toggleDayStatus(day)">
                      <div class="switch-handle" :class="{ active: day.isOpen }"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button class="btn btn-cancel" @click="handleClose">取消</button>
        <button class="btn btn-primary" @click="handleSave">{{ isEdit ? '保存' : '创建' }}</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'

// 定义接口
interface Shop {
  id: number
  name: string
  subName: string
  manager: string
  phone: string
  address: string
  addressDetail: string
  servicePhone: string
  status: 'active' | 'pending'
  businessHours: string
  createTime: string
  password?: string
  longitude?: string
  latitude?: string
  description?: string
}

interface FormData {
  name: string
  manager: string
  phone: string
  address: string
  servicePhone: string
  status: 'active' | 'pending'
  password: string
  longitude: string
  latitude: string
  description: string
}

interface WeekDay {
  key: string
  label: string
  isOpen: boolean
  startTime: string
  endTime: string
}

// 定义 props
interface Props {
  visible: boolean
  isEdit: boolean
  editingShop?: Shop | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  editingShop: null
})

// 定义 emits
const emit = defineEmits<{
  close: []
  save: [formData: FormData, weekDays: WeekDay[]]
}>()

// 响应式数据
const activeTab = ref<'basic' | 'hours'>('basic')

const formData = reactive<FormData>({
  name: '',
  manager: '',
  phone: '',
  address: '',
  servicePhone: '',
  status: 'active',
  password: '',
  longitude: '',
  latitude: '',
  description: ''
})

const weekDays = reactive<WeekDay[]>([
  { key: 'monday', label: '周一', isOpen: true, startTime: '09:00', endTime: '22:00' },
  { key: 'tuesday', label: '周二', isOpen: true, startTime: '09:00', endTime: '22:00' },
  { key: 'wednesday', label: '周三', isOpen: true, startTime: '09:00', endTime: '22:00' },
  { key: 'thursday', label: '周四', isOpen: true, startTime: '09:00', endTime: '22:00' },
  { key: 'friday', label: '周五', isOpen: true, startTime: '09:00', endTime: '22:00' },
  { key: 'saturday', label: '周六', isOpen: true, startTime: '09:00', endTime: '22:00' },
  { key: 'sunday', label: '周日', isOpen: true, startTime: '09:00', endTime: '22:00' }
])

// 方法
const resetFormData = () => {
  Object.assign(formData, {
    name: '',
    manager: '',
    phone: '',
    address: '',
    servicePhone: '',
    status: 'active',
    password: '',
    longitude: '',
    latitude: '',
    description: ''
  })
  
  weekDays.forEach(day => {
    day.isOpen = true
    day.startTime = '09:00'
    day.endTime = '22:00'
  })
}

const fillFormData = (shop: Shop) => {
  Object.assign(formData, {
    name: shop.name,
    manager: shop.manager,
    phone: shop.phone,
    address: shop.address,
    servicePhone: shop.servicePhone,
    status: shop.status,
    password: shop.password || '',
    longitude: shop.longitude || '',
    latitude: shop.latitude || '',
    description: shop.description || ''
  })
}

const toggleDayStatus = (day: WeekDay) => {
  day.isOpen = !day.isOpen
}

const handleClose = () => {
  emit('close')
}

const handleSave = () => {
  // 简单验证
  if (!formData.name || !formData.manager || !formData.phone || !formData.address) {
    alert('请填写必填字段')
    return
  }

  emit('save', formData, weekDays)
}

// 监听 props 变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    activeTab.value = 'basic'
    if (props.isEdit && props.editingShop) {
      fillFormData(props.editingShop)
    } else {
      resetFormData()
    }
  }
})
</script>

<style scoped>
/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-container {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #ebeef5;
  background: #fafbfc;
}

.dialog-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #909399;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f5f7fa;
  color: #606266;
}

.dialog-body {
  max-height: 70vh;
  overflow-y: auto;
  min-height: 400px;
  background: #fff;
}

/* Tab 导航 */
.tab-navigation {
  display: flex;
  border-bottom: 1px solid #ebeef5;
  background: #fafbfc;
}

.tab-btn {
  /* flex: 1; */
  padding: 16px 20px;
  border: none;
  background: none;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
}

.tab-btn:hover {
  color: #415A77;
  background: rgba(65, 90, 119, 0.05);
}

.tab-btn.active {
  color: #415A77;
  background: white;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #415A77;
  border-radius: 3px 3px 0 0;
}

.tab-icon {
  font-size: 16px;
}

/* Tab 内容 */
.tab-content {
  padding: 24px;
  min-height: 400px;
  background: white;
}

.tab-panel {
  animation: fadeIn 0.3s ease;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 表单样式 */
/* .form-section {
  margin-bottom: 32px;
} */

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-left: 12px;
  border-left: 4px solid #415A77;
}

.section-desc {
  font-size: 14px;
  color: #909399;
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.form-field {
  display: flex;
  flex-direction: column;
}

.form-field.full-width {
  grid-column: 1 / -1;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.required {
  color: #f56c6c;
}

.form-input,
.form-select,
.form-textarea {
  padding: 12px 16px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #415A77;
  box-shadow: 0 0 0 3px rgba(65, 90, 119, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.address-input-group {
  display: flex;
  gap: 8px;
}

.address-input-group .form-input {
  flex: 1;
}

.location-btn {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 12px 16px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.location-btn:hover {
  background: #ebeef5;
  color: #415A77;
}

/* 营业时间设置 */
.hours-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  margin-bottom: 12px;
}

.day-label {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  min-width: 60px;
}

.hours-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.status-tag.active {
  background: #f0f9ff;
  color: #67C23A;
  border: 1px solid #b3e19d;
}

.status-tag.closed {
  background: #f5f7fa;
  color: #909399;
  border: 1px solid #dcdfe6;
}

.time-input {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.time-input:focus {
  outline: none;
  border-color: #415A77;
}

.time-separator {
  font-size: 14px;
  color: #909399;
}

/* 切换开关 */
.toggle-switch {
  width: 48px;
  height: 24px;
  background: #dcdfe6;
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-switch:hover {
  background: #c0c4cc;
}

.switch-handle {
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.switch-handle.active {
  transform: translateX(24px);
}

.toggle-switch.active {
  background: #67C23A;
}

/* 对话框底部 */
.dialog-footer {
  padding: 16px 24px;
  border-top: 1px solid #ebeef5;
  background: #fafbfc;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid;
}

.btn-cancel {
  background: white;
  color: #606266;
  border-color: #dcdfe6;
}

.btn-cancel:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
}

.btn-primary {
  background: #415A77;
  color: white;
  border-color: #415A77;
}

.btn-primary:hover {
  background: #1B365D;
  border-color: #1B365D;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dialog-container {
    width: 95%;
    max-height: 95vh;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .tab-navigation {
    flex-direction: column;
  }

  .hours-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .hours-controls {
    justify-content: space-between;
  }
}
</style>
