

[TOC]
    
##### 简要描述

- 完整的接口请求url由`基础URL`和`接口名`两部分组成；直接在`基础URL`后拼接`接口名`即可；`基础URL`建议单一保存设置，方便维护。
- 具体的接口文档中只会给出接口名。

##### 基础URL格式
- ` http://jiuruan-saas.zongruankj.com:39666/ `

##### 接口名格式
- ` admin/Login/login`

##### 完整的接口请求URL格式
- ` http://jiuruan-saas.zongruankj.com:39666/admin/Login/login `
  
##### 请求方式
- 本系统接口方式分为两种：`GET`和`POST`，具有以下规则：
- `列表接口`和`查看接口`一般为`GET`；其余的接口均为`POST`，具体以文档为准

##### 每个接口统一的header参数

- 每个接口都需要在header中传入这四个字段（token除了注册和登录接口外都需要传入），具体的文档中不再赘写这四个header。

|参数名|必选|类型|说明|
|:----    |:---|:----- |-----   |
|Accept |是  |string |值固定为：`application/json`   |
|token |是  |string | 具体账号登录后返回的token字段    |
|timestamp |是  |int | 当前的时间戳，精确到秒    |
|apikey |是  |string | 当前的timestamp拼接字符串`ca89ed`，然后进行md5加密，将加密后的结果再与字符串`a77f0a3eb0955b2147dabaae883848af`拼接，然后再进行md5加密；注意：所有的md5加密结果均取小写结果    |

##### 列表接口的基础参数

- 每个列表页接口都会有以下四个字段，具体的文档中不再赘写这四个字段。

|参数名|必选|类型|说明|
|:----    |:---|:----- |-----   |
|page |是  |int |当前页码，默认为1   |
|page_size |是  |int | 每页显示的数量，默认为10    |
|sort_field |是  |string | 排序字段，默认为id    |
|sort |是  |string | 排序规则，默认为倒序   |

##### 返回示例 

``` 
 {
	"code": 1,
	"msg": "ok",
	"data": {
		"itemList": [{
			"id": 2,
			"trade_name": "茶楼",
			"sort": 1,
			"created_at": "2025-02-02 15:56:36",
			"updated_at": "2025-02-02 15:56:36"
		}, {
			"id": 3,
			"trade_name": "茶叶店",
			"sort": 2,
			"created_at": "2025-02-02 15:57:24",
			"updated_at": "2025-02-02 15:57:24"
		}, {
			"id": 6,
			"trade_name": "其他",
			"sort": 50,
			"created_at": "2025-02-02 15:58:22",
			"updated_at": "2025-02-02 15:58:22"
		}],
		"total": 6,
		"pageSize": "20"
	}
}
```

##### 返回参数说明 

- 具体的文档中不会赘写着三个返回字段，只会给出返回成功的数据

|参数名|类型|说明|
|:-----  |:-----|-----                           |
|code |int   |接口返回码，成功为：1；错误为：2；当该值为2时，没有`data`字段  |
|msg |string   |接口提示信息，当接口报错时，该值可作为提示信息  |
|data |mixed   |接口成功返回时的数据，具体返回类型以具体文档为准  |

##### 备注 

- 更多返回错误代码请看首页的错误代码描述




