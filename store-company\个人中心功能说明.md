# 个人中心功能说明

## 功能概述

个人中心对话框已成功构建，提供用户个人信息的查看和编辑功能。

## 功能特点

### 1. 字段显示
- **昵称**：可编辑，必填字段
- **登录账号**：只读显示，不可修改
- **登录密码**：可编辑，可选字段（留空表示不修改密码）
- **所属角色**：只读显示，不可修改

### 2. 交互设计
- 点击右上角用户头像下拉菜单中的"个人中心"打开对话框
- 模态框设计，点击遮罩层或关闭按钮可关闭
- 密码字段支持显示/隐藏切换
- 表单验证和错误提示
- 保存时显示加载状态

### 3. 样式特点
- 采用商务典雅蓝色主题系统
- 响应式设计，支持移动端
- 与系统其他对话框保持一致的设计风格
- 清晰的视觉层次和交互反馈

## 技术实现

### 组件结构
```
store-company/src/components/ProfileDialog.vue  # 个人中心对话框组件
store-company/src/components/Header.vue         # 头部组件（包含触发入口）
store-company/src/stores/auth.ts               # 用户认证状态管理
```

### 主要功能
1. **数据获取**：从 auth store 获取当前用户信息
2. **表单验证**：昵称必填，密码可选但有长度限制
3. **数据保存**：支持昵称和密码的修改
4. **状态同步**：保存后自动更新 store 中的用户信息
5. **消息提示**：使用统一的消息提示组件

### 验证规则
- **昵称**：必填，长度2-20个字符
- **密码**：可选，如果填写则长度6-20个字符
- **登录账号**：只读，不可修改
- **所属角色**：只读，不可修改

## 使用方法

1. 登录系统后，点击右上角用户头像
2. 在下拉菜单中选择"个人中心"
3. 在弹出的对话框中修改昵称或密码
4. 点击"保存修改"按钮提交更改
5. 系统会显示保存结果并自动关闭对话框

## 安全特性

1. **密码处理**：
   - 密码字段支持显示/隐藏切换
   - 留空表示不修改当前密码
   - 输入新密码时会进行长度验证

2. **权限控制**：
   - 只能修改自己的个人信息
   - 登录账号和角色信息只读

3. **数据验证**：
   - 前端表单验证
   - 后端API验证（待集成）

## 后续优化

1. **API集成**：连接后端个人资料修改接口
2. **头像上传**：添加头像上传功能
3. **密码强度**：添加密码强度检测
4. **操作日志**：记录个人信息修改日志
5. **二次确认**：重要信息修改时添加二次确认

## 注意事项

1. 当前为前端演示版本，数据保存在内存中
2. 刷新页面会重置到初始状态
3. 实际部署时需要连接后端API接口
4. 密码修改建议添加原密码验证步骤
