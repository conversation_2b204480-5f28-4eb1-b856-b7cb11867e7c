<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <!-- <div class="auth-logo">🏢</div> -->
        <h1 class="auth-title">企业注册</h1>
        <!-- <p class="auth-subtitle">开启您的数字化管理之旅</p> -->
      </div>
      
      <form @submit.prevent="handleRegister" class="auth-form">
        <div class="form-group">
          <label class="form-label">企业名称</label>
          <input 
            type="text" 
            class="form-input" 
            placeholder="请输入企业名称"
            v-model="registerForm.companyName"
          />
        </div>
        
        <!-- <div class="form-group">
          <label class="form-label">联系人姓名</label>
          <input 
            type="text" 
            class="form-input" 
            placeholder="请输入联系人姓名"
            v-model="registerForm.contactName"
          />
        </div> -->
        
        <div class="form-group">
          <label class="form-label">手机号码</label>
          <input 
            type="tel" 
            class="form-input" 
            placeholder="请输入手机号码"
            v-model="registerForm.phone"
          />
        </div>
        
        <div class="form-group">
          <label class="form-label">短信验证码</label>
          <div class="captcha-container">
            <input 
              type="text" 
              class="form-input captcha-input" 
              placeholder="请输入短信验证码"
              v-model="registerForm.smsCode"
            />
            <button 
              type="button" 
              class="captcha-btn" 
              :disabled="smsCountdown > 0"
              @click="sendSmsCode"
            >
              {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '获取验证码' }}
            </button>
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">登录密码</label>
          <div class="password-container">
            <input 
              :type="showPassword ? 'text' : 'password'" 
              class="form-input" 
              placeholder="请设置登录密码"
              v-model="registerForm.password"
            />
            <button 
              type="button" 
              class="password-toggle" 
              @click="togglePassword"
            >
              {{ showPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
          <!-- <div class="password-strength" v-if="registerForm.password">
            <div 
              class="password-strength-bar" 
              :class="passwordStrength.level"
            ></div>
            <div class="password-strength-text">
              密码强度：{{ passwordStrength.text }}
            </div>
          </div> -->
        </div>
        
        <div class="form-group">
          <label class="form-label">确认密码</label>
          <div class="password-container">
            <input 
              :type="showConfirmPassword ? 'text' : 'password'" 
              class="form-input" 
              placeholder="请确认登录密码"
              v-model="registerForm.confirmPassword"
            />
            <button 
              type="button" 
              class="password-toggle" 
              @click="toggleConfirmPassword"
            >
              {{ showConfirmPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
        </div>
        
        <div class="agreement">
          <label class="checkbox-label">
            <input type="checkbox" v-model="registerForm.agreement">
            我已阅读并同意
            <a href="#" @click.prevent="viewAgreement">《用户协议》</a>
            和
            <a href="#" @click.prevent="viewPrivacy">《隐私政策》</a>
          </label>
        </div>
        
        <button type="submit" class="submit-btn" :disabled="loading">
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? '注册中...' : '立即注册' }}
        </button>
        
        <div class="auth-switch">
          <span class="auth-switch-text">已有账户？</span>
          <router-link to="/login" class="auth-switch-link">立即登录</router-link>
        </div>
      </form>
    </div>
    
    <!-- User Agreement Dialog -->
    <HtmlContentDialog
      v-model:visible="showAgreementDialog"
      title="用户协议"
      :content="userAgreementContent"
      @close="showAgreementDialog = false"
    />
    
    <!-- Privacy Policy Dialog -->
    <HtmlContentDialog
      v-model:visible="showPrivacyDialog"
      title="隐私政策"
      :content="privacyPolicyContent"
      @close="showPrivacyDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import HtmlContentDialog from '@/components/HtmlContentDialog.vue'

const router = useRouter()
const authStore = useAuthStore()

const registerForm = ref({
  companyName: '',
  contactName: '',
  phone: '',
  smsCode: '',
  password: '',
  confirmPassword: '',
  agreement: false
})

const loading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const smsCountdown = ref(0)

// Dialog states
const showAgreementDialog = ref(false)
const showPrivacyDialog = ref(false)

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

// 密码强度检测
const passwordStrength = computed(() => {
  const password = registerForm.value.password.trim()
  if (!password) return { level: '', text: '' }
  
  let score = 0
  if (password.length >= 8) score++
  if (/[a-z]/.test(password)) score++
  if (/[A-Z]/.test(password)) score++
  if (/[0-9]/.test(password)) score++
  if (/[^a-zA-Z0-9]/.test(password)) score++
  
  if (score <= 2) {
    return { level: 'weak', text: '弱' }
  } else if (score <= 3) {
    return { level: 'medium', text: '中' }
  } else {
    return { level: 'strong', text: '强' }
  }
})

// 发送短信验证码
const sendSmsCode = () => {
  if (!registerForm.value.phone.trim().trim()) {
    ElMessage.warning('请先输入手机号码')
    return
  }
  
  if (!/^1[3-9]\d{9}$/.test(registerForm.value.phone.trim())) {
    ElMessage.error('请输入正确的手机号码')
    return
  }
  
  // 模拟发送短信验证码
  smsCountdown.value = 60
  ElMessage.success('验证码发送成功')
  
  const timer = setInterval(() => {
    smsCountdown.value--
    if (smsCountdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// HTML content for dialogs
const userAgreementContent = `
<h1>用户协议</h1>

<p><strong>生效日期：</strong>2024年1月1日</p>

<h2>1. 协议的接受</h2>
<p>欢迎使用我们的企业管理平台服务。通过注册、访问或使用本服务，您表示同意受本用户协议（"协议"）的约束。如果您不同意本协议的条款，请不要使用本服务。</p>

<h2>2. 服务描述</h2>
<p>本平台为企业用户提供以下服务：</p>
<ul>
  <li><strong>企业管理工具：</strong>包括但不限于员工管理、项目管理、财务管理等功能</li>
  <li><strong>数据分析服务：</strong>为企业提供业务数据分析和报告</li>
  <li><strong>云存储服务：</strong>安全可靠的企业数据存储解决方案</li>
  <li><strong>协作工具：</strong>团队协作和沟通工具</li>
</ul>

<h2>3. 用户责任</h2>
<p>作为本服务的用户，您同意：</p>
<ol>
  <li>提供真实、准确、完整的注册信息</li>
  <li>维护账户信息的安全性和保密性</li>
  <li>不得将账户转让给第三方</li>
  <li>遵守所有适用的法律法规</li>
  <li>不得从事任何可能损害平台或其他用户利益的行为</li>
</ol>

<h2>4. 知识产权</h2>
<p>本平台及其所有内容、功能和服务均受版权、商标和其他知识产权法律保护。未经明确书面许可，您不得复制、修改、分发或以其他方式使用本平台的任何部分。</p>

<h2>5. 隐私保护</h2>
<p>我们重视您的隐私权。有关我们如何收集、使用和保护您的个人信息的详细信息，请参阅我们的<a href="#" onclick="return false;">隐私政策</a>。</p>

<h2>6. 服务变更</h2>
<p>我们保留随时修改、暂停或终止服务的权利，恕不另行通知。我们将尽力提前通知重大变更。</p>

<h2>7. 免责声明</h2>
<p>本服务按"现状"提供，我们不对服务的可用性、准确性或完整性做出任何明示或暗示的保证。</p>

<h2>8. 联系我们</h2>
<p>如果您对本协议有任何疑问，请通过以下方式联系我们：</p>
<ul>
  <li>邮箱：<EMAIL></li>
  <li>电话：400-123-4567</li>
  <li>地址：北京市朝阳区xxx路xxx号</li>
</ul>
`

const privacyPolicyContent = `
<h1>隐私政策</h1>

<p><strong>最后更新日期：</strong>2024年1月1日</p>

<h2>1. 信息收集</h2>
<p>我们可能收集以下类型的信息：</p>

<h3>1.1 个人信息</h3>
<ul>
  <li><strong>注册信息：</strong>企业名称、联系人姓名、电话号码、邮箱地址</li>
  <li><strong>身份验证信息：</strong>用户名、密码（加密存储）</li>
  <li><strong>企业信息：</strong>营业执照、组织机构代码等企业资质信息</li>
</ul>

<h3>1.2 使用信息</h3>
<ul>
  <li><strong>日志信息：</strong>IP地址、浏览器类型、访问时间、页面访问记录</li>
  <li><strong>设备信息：</strong>设备类型、操作系统、设备标识符</li>
  <li><strong>使用数据：</strong>功能使用情况、操作记录</li>
</ul>

<h2>2. 信息使用</h2>
<p>我们使用收集的信息用于：</p>
<ol>
  <li><strong>提供服务：</strong>为您提供平台功能和技术支持</li>
  <li><strong>账户管理：</strong>创建和维护您的账户</li>
  <li><strong>安全保护：</strong>检测和防范安全威胁</li>
  <li><strong>服务改进：</strong>分析使用情况以改进我们的服务</li>
  <li><strong>法律合规：</strong>遵守适用的法律法规要求</li>
</ol>

<h2>3. 信息共享</h2>
<p>我们不会出售、租赁或以其他方式向第三方披露您的个人信息，除非：</p>
<ul>
  <li>获得您的明确同意</li>
  <li>法律法规要求</li>
  <li>保护我们的合法权益</li>
  <li>与可信的服务提供商合作（在严格的保密协议下）</li>
</ul>

<h2>4. 数据安全</h2>
<p>我们采取以下措施保护您的信息安全：</p>
<ul>
  <li><strong>加密传输：</strong>使用SSL/TLS加密技术保护数据传输</li>
  <li><strong>访问控制：</strong>严格限制对个人信息的访问权限</li>
  <li><strong>安全审计：</strong>定期进行安全评估和漏洞扫描</li>
  <li><strong>数据备份：</strong>建立完善的数据备份和恢复机制</li>
</ul>

<h2>5. Cookie使用</h2>
<p>我们使用Cookie和类似技术来：</p>
<ul>
  <li>记住您的登录状态</li>
  <li>分析网站使用情况</li>
  <li>提供个性化体验</li>
  <li>改进服务质量</li>
</ul>
<p>您可以通过浏览器设置管理Cookie偏好。</p>

<h2>6. 数据保留</h2>
<p>我们将在以下期限内保留您的个人信息：</p>
<ul>
  <li><strong>账户信息：</strong>账户存续期间及注销后3年</li>
  <li><strong>交易记录：</strong>根据法律要求保留相应期限</li>
  <li><strong>日志信息：</strong>通常保留6个月</li>
</ul>

<h2>7. 您的权利</h2>
<p>根据适用的法律法规，您享有以下权利：</p>
<ul>
  <li><strong>访问权：</strong>查看我们持有的您的个人信息</li>
  <li><strong>更正权：</strong>要求更正不准确的个人信息</li>
  <li><strong>删除权：</strong>在特定情况下要求删除个人信息</li>
  <li><strong>限制处理权：</strong>限制我们处理您的个人信息</li>
  <li><strong>数据可携权：</strong>以结构化格式获取您的个人信息</li>
</ul>

<h2>8. 未成年人保护</h2>
<p>我们的服务面向企业用户，不会故意收集未满18周岁未成年人的个人信息。如果我们发现收集了未成年人的信息，将立即删除。</p>

<h2>9. 政策更新</h2>
<p>我们可能会不时更新本隐私政策。重大变更将通过网站公告或邮件通知您。继续使用服务即表示您接受更新后的政策。</p>

<h2>10. 联系我们</h2>
<p>如果您对本隐私政策有任何疑问或需要行使您的权利，请联系我们：</p>
<ul>
  <li><strong>数据保护官邮箱：</strong><EMAIL></li>
  <li><strong>客服电话：</strong>400-123-4567</li>
  <li><strong>通信地址：</strong>北京市朝阳区xxx路xxx号</li>
</ul>
`

const viewAgreement = () => {
  showAgreementDialog.value = true
}

const viewPrivacy = () => {
  showPrivacyDialog.value = true
}

const handleRegister = async () => {
  // 基本验证
  if (!registerForm.value.companyName.trim()) {
    ElMessage.warning('请输入企业名称')
    return
  }
  
  /* if (!registerForm.value.contactName.trim()) {
    ElMessage.warning('请输入联系人姓名')
    return
  } */
  
  if (!registerForm.value.phone.trim() || !/^1[3-9]\d{9}$/.test(registerForm.value.phone.trim())) {
    ElMessage.warning('请输入正确的手机号码')
    return
  }
  
  if (!registerForm.value.smsCode.trim()) {
    ElMessage.warning('请输入短信验证码')
    return
  }
  
  if (!registerForm.value.password.trim() || registerForm.value.password.trim().length < 6) {
    ElMessage.warning('请输入至少6位密码')
    return
  }
  
  if (registerForm.value.password.trim() !== registerForm.value.confirmPassword.trim()) {
    ElMessage.warning('两次输入的密码不一致')
    return
  }
  
  if (!registerForm.value.agreement) {
    ElMessage.warning('请阅读并同意用户协议和隐私政策')
    return
  }
  
  loading.value = true
  
  try {
    // 模拟注册API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('注册成功！请登录')
    router.push('/login')
  } catch (error) {
    ElMessage.error('注册失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0D1B2A 0%, #1B365D 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: hidden;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(ellipse at 25% 20%, rgba(232, 184, 109, 0.08) 0%, transparent 60%),
    radial-gradient(ellipse at 75% 80%, rgba(199, 210, 221, 0.12) 0%, transparent 60%),
    radial-gradient(ellipse at 50% 50%, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(0.5deg); }
  66% { transform: translate(-20px, 20px) rotate(-0.5deg); }
}

.auth-card {
  background: rgba(255, 255, 255, 0.92);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 24px;
  box-shadow: 
    0 8px 32px rgba(13, 27, 42, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  width: 100%;
  max-width: 520px;
  padding: 48px 40px;
  position: relative;
  z-index: 5;
  animation: cardSlideUp 0.8s ease-out;
}

@keyframes cardSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 40px;
}

.auth-logo {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #E8B86D 0%, #B8C5D1 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  box-shadow: 0 4px 16px rgba(13, 27, 42, 0.15);
  font-size: 36px;
  color: #0D1B2A;
  font-weight: bold;
}

.auth-title {
  font-size: 28px;
  font-weight: 600;
  color: #0D1B2A;
  margin-bottom: 8px;
  line-height: 1.2;
}

.auth-subtitle {
  font-size: 16px;
  color: #778DA9;
  font-weight: 400;
}

.auth-form {
  width: 100%;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 15px;
  font-weight: 500;
  color: #415A77;
}

.form-input {
  width: 100%;
  height: 48px;
  border: 2px solid rgba(65, 90, 119, 0.1);
  border-radius: 12px;
  padding: 0 16px;
  font-size: 16px;
  color: #0D1B2A;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #415A77;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 4px rgba(65, 90, 119, 0.1);
}

.form-input::placeholder {
  color: #B8C5D1;
}

/* 密码容器 */
.password-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #778DA9;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
}

.password-toggle:hover {
  color: #415A77;
}

/* 验证码容器 */
.captcha-container {
  display: flex;
  gap: 12px;
}

.captcha-input {
  flex: 1;
}

.captcha-btn {
  width: 120px;
  height: 48px;
  background: linear-gradient(135deg, #E8B86D 0%, #B8C5D1 100%);
  border: none;
  border-radius: 12px;
  color: #0D1B2A;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.captcha-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 27, 42, 0.15);
}

.captcha-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 8px;
  height: 4px;
  background: rgba(65, 90, 119, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.password-strength-bar {
  height: 100%;
  width: 0%;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.password-strength-bar.weak {
  width: 33%;
  background: #f56565;
}

.password-strength-bar.medium {
  width: 66%;
  background: #ed8936;
}

.password-strength-bar.strong {
  width: 100%;
  background: #48bb78;
}

.password-strength-text {
  font-size: 12px;
  margin-top: 4px;
  color: #B8C5D1;
}

/* 协议同意 */
.agreement {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  font-size: 14px;
  color: #415A77;
  line-height: 1.5;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #415A77;
  cursor: pointer;
  line-height: 1.5;
}

.checkbox-label input {
  margin-top: 2px;
  flex-shrink: 0;
}

.agreement a {
  color: #415A77;
  text-decoration: none;
  margin: 0 4px;
}

.agreement a:hover {
  text-decoration: underline;
}

.submit-btn {
  width: 100%;
  height: 52px;
  background: linear-gradient(135deg, #1B365D 0%, #0D1B2A 100%);
  border: none;
  border-radius: 14px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(13, 27, 42, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(13, 27, 42, 0.3);
}

.submit-btn:active {
  transform: translateY(0);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 页面切换链接样式 */
.auth-switch {
  text-align: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid rgba(65, 90, 119, 0.1);
}

.auth-switch-text {
  color: #778DA9;
  font-size: 14px;
  margin-right: 8px;
}

.auth-switch-link {
  color: #415A77;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.auth-switch-link:hover {
  color: #0D1B2A;
  text-decoration: underline;
}

/* 加载状态 */
.submit-btn.loading {
  pointer-events: none;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-card {
    padding: 32px 24px;
    margin: 16px;
  }

  .auth-title {
    font-size: 24px;
  }

  .auth-subtitle {
    font-size: 14px;
  }
  
  .captcha-container {
    flex-direction: column;
  }
  
  .captcha-btn {
    width: 100%;
  }
}
</style>